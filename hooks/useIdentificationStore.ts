import { useState, useEffect } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { Plant, IdentificationResult } from '@/types/plant';
import { OpenRouterService, PlantIdentificationData, DiagnosisData } from '@/services/openrouter';
import { DatabaseService, PlantIdentification, PlantDiagnosis } from '@/services/database';
import { useAuth } from './useAuth';
import { StorageService } from '@/services/r2Storage';

export const [IdentificationProvider, useIdentification] = createContextHook(() => {
  const [results, setResults] = useState<IdentificationResult[]>([]);
  const [currentResult, setCurrentResult] = useState<IdentificationResult | null>(null);
  const [scanResult, setScanResult] = useState<IdentificationResult | null>(null);
  const [diagnoseResult, setDiagnoseResult] = useState<IdentificationResult | null>(null);
  const [isIdentifying, setIsIdentifying] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [isDiagnosing, setIsDiagnosing] = useState(false);
  const [recentIdentifications, setRecentIdentifications] = useState<PlantIdentification[]>([]);
  const [recentDiagnoses, setRecentDiagnoses] = useState<PlantDiagnosis[]>([]);
  const [isLoadingRecent, setIsLoadingRecent] = useState(false);
  const [isLoadingRecentDiagnoses, setIsLoadingRecentDiagnoses] = useState(false);
  const { user } = useAuth();

  // Load recent identifications when user changes
  useEffect(() => {
    const loadRecentIdentifications = async () => {
      if (!user) {
        setRecentIdentifications([]);
        return;
      }

      setIsLoadingRecent(true);
      try {
        const recent = await DatabaseService.getRecentPlantIdentifications(user.id, 5);
        setRecentIdentifications(recent);
      } catch (error) {
        console.error('Error loading recent identifications:', error);
      } finally {
        setIsLoadingRecent(false);
      }
    };

    loadRecentIdentifications();
  }, [user]);

  // Load recent diagnoses when user changes
  useEffect(() => {
    const loadRecentDiagnoses = async () => {
      if (!user) {
        setRecentDiagnoses([]);
        return;
      }

      setIsLoadingRecentDiagnoses(true);
      try {
        const recent = await DatabaseService.getRecentPlantDiagnoses(user.id, 5);
        setRecentDiagnoses(recent);
      } catch (error) {
        console.error('Error loading recent diagnoses:', error);
      } finally {
        setIsLoadingRecentDiagnoses(false);
      }
    };

    loadRecentDiagnoses();
  }, [user]);

  // Function to refresh recent identifications
  const refreshRecentIdentifications = async () => {
    if (!user) return;

    setIsLoadingRecent(true);
    try {
      const recent = await DatabaseService.getRecentPlantIdentifications(user.id, 5);
      setRecentIdentifications(recent);
    } catch (error) {
      console.error('Error refreshing recent identifications:', error);
    } finally {
      setIsLoadingRecent(false);
    }
  };

  // Function to refresh recent diagnoses
  const refreshRecentDiagnoses = async () => {
    if (!user) return;

    setIsLoadingRecentDiagnoses(true);
    try {
      const recent = await DatabaseService.getRecentPlantDiagnoses(user.id, 5);
      setRecentDiagnoses(recent);
    } catch (error) {
      console.error('Error refreshing recent diagnoses:', error);
    } finally {
      setIsLoadingRecentDiagnoses(false);
    }
  };

  // Helper function to upload image and get URL
  const uploadImageToStorage = async (imageUri: string, type: 'scan' | 'diagnosis'): Promise<string> => {
    try {
      // If it's already a URL, return as is
      if (imageUri.startsWith('http://') || imageUri.startsWith('https://')) {
        console.log('Image is already a URL, using as-is:', imageUri);
        return imageUri;
      }

      // If it's already a data URL, return as is
      if (imageUri.startsWith('data:')) {
        console.log('Image is already a data URL, using as-is');
        return imageUri;
      }

      console.log('Converting image URI to blob for upload:', imageUri);

      // Convert URI to blob
      const response = await fetch(imageUri);
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      console.log('Blob created for upload:', { size: blob.size, type: blob.type });

      // Generate unique path
      const fileExt = imageUri.split('.').pop() || 'jpg';
      const path = StorageService.generateImagePath(user?.id || 'anonymous', type, fileExt);

      console.log('Attempting to upload image to storage:', { path, type });

      // Upload to storage
      const uploadResult = await StorageService.uploadImage(blob, path, blob.type);

      if (uploadResult.success && uploadResult.url) {
        console.log('Image upload successful:', uploadResult.url);
        return uploadResult.url;
      } else {
        console.warn('Image upload failed, using original URI:', uploadResult.error);
        return imageUri; // Fallback to original URI
      }
    } catch (error) {
      console.error('Error uploading image:', error);
      console.log('Falling back to original URI due to upload error');
      return imageUri; // Fallback to original URI
    }
  };

  // Plant identification for scan page (regular identification)
  const identifyPlantForScan = async (imageUri: string, location?: string): Promise<IdentificationResult> => {
    setIsScanning(true);
    setIsIdentifying(true);

    try {
      const identificationData = await OpenRouterService.identifyPlant(imageUri);

      // Convert OpenRouter response to Plant format
      const plant: Plant = {
        id: `plant_${Date.now()}`, // Generate unique ID
        scientificName: identificationData.scientificName,
        commonName: identificationData.commonName,
        imageUrl: imageUri, // Use the captured image
        description: identificationData.description,
        careInstructions: identificationData.careInstructions,
        tags: identificationData.tags,
      };

      // Save to database if user is authenticated
      if (user) {
        try {
          // Try to upload image to storage, but don't fail if it doesn't work
          let finalImageUrl = imageUri;
          try {
            finalImageUrl = await uploadImageToStorage(imageUri, 'scan');
          } catch (uploadError) {
            console.warn('Image upload failed, proceeding with original URI:', uploadError);
            // Continue with original imageUri
          }

          await DatabaseService.createPlantIdentification({
            user_id: user.id,
            image_url: finalImageUrl,
            scientific_name: identificationData.scientificName,
            common_name: identificationData.commonName,
            description: identificationData.description,
            care_instructions: JSON.stringify(identificationData.careInstructions),
            tags: identificationData.tags,
            confidence_score: identificationData.confidence,
            identification_source: 'ai',
            is_verified: false,
            is_public: false,
            location_taken: location,
          });

          // Refresh recent identifications and update profile stats after saving
          refreshRecentIdentifications();
          DatabaseService.updateUserProfileStats(user.id);
        } catch (dbError) {
          console.error('Error saving identification to database:', dbError);
          // Continue with local result even if database save fails
        }
      }

      const result: IdentificationResult = {
        plant,
        confidence: identificationData.confidence,
        timestamp: new Date(),
        imageUri,
        identificationData,
      };

      setResults(prev => [result, ...prev]);
      setScanResult(result);
      setCurrentResult(result); // Keep for backward compatibility

      return result;
    } catch (error) {
      console.error('Plant identification failed:', error);
      throw error;
    } finally {
      setIsScanning(false);
      setIsIdentifying(false);
    }
  };

  // Plant diagnosis for diagnose page (always uses diagnosis mode)
  const diagnosePlantForDiagnose = async (imageUri: string, problemDescription?: string, location?: string): Promise<IdentificationResult> => {
    setIsDiagnosing(true);
    setIsIdentifying(true);

    try {
      // Always use diagnosis mode for the diagnose page
      const identificationData = await OpenRouterService.diagnosePlant(imageUri, problemDescription);

      // Convert OpenRouter response to Plant format
      const plant: Plant = {
        id: `plant_${Date.now()}`, // Generate unique ID
        scientificName: identificationData.scientificName,
        commonName: identificationData.commonName,
        imageUrl: imageUri, // Use the captured image
        description: identificationData.description,
        careInstructions: identificationData.careInstructions,
        tags: identificationData.tags,
      };

      // Save to database if user is authenticated
      let savedDiagnosis = null;
      if (user) {
        try {
          // Try to upload image to storage, but don't fail if it doesn't work
          let finalImageUrl = imageUri;
          try {
            finalImageUrl = await uploadImageToStorage(imageUri, 'diagnosis');
          } catch (uploadError) {
            console.warn('Image upload failed, proceeding with original URI:', uploadError);
            // Continue with original imageUri
          }

          // First create plant identification
          const plantIdentification = await DatabaseService.createPlantIdentification({
            user_id: user.id,
            image_url: finalImageUrl,
            scientific_name: identificationData.scientificName,
            common_name: identificationData.commonName,
            description: identificationData.description,
            care_instructions: JSON.stringify(identificationData.careInstructions),
            tags: identificationData.tags,
            confidence_score: identificationData.confidence,
            identification_source: 'ai',
            is_verified: false,
            is_public: false,
            location_taken: location,
          });

          // Then create diagnosis
          if (plantIdentification) {
            // Normalize severity to match database constraint
            const normalizedSeverity = identificationData.severity?.toLowerCase() as 'mild' | 'moderate' | 'severe' | 'critical';

            savedDiagnosis = await DatabaseService.createPlantDiagnosis({
              user_id: user.id,
              plant_identification_id: plantIdentification.id,
              image_url: imageUri,
              problem_description: problemDescription,
              diagnosed_problem: identificationData.diagnosedProblem,
              likely_causes: identificationData.likelyCauses,
              symptoms_observed: identificationData.symptomsObserved,
              severity: normalizedSeverity,
              immediate_actions: identificationData.immediateActions,
              long_term_care: identificationData.longTermCare,
              product_recommendations: identificationData.productRecommendations,
              step_by_step_instructions: identificationData.stepByStepInstructions,
              prevention_tips: identificationData.preventionTips,
              prognosis: identificationData.prognosis,
              confidence_score: identificationData.confidence,
              diagnosis_source: 'ai',
              is_verified: false,
              is_public: false,
            });
          }

          // Refresh recent identifications and diagnoses, and update profile stats after saving
          refreshRecentIdentifications();
          refreshRecentDiagnoses();
          DatabaseService.updateUserProfileStats(user.id);
        } catch (dbError) {
          console.error('Error saving diagnosis to database:', dbError);
          // Continue with local result even if database save fails
        }
      }

      const result: IdentificationResult = {
        plant,
        confidence: identificationData.confidence,
        timestamp: new Date(),
        imageUri,
        problemDescription,
        identificationData,
        // Always include diagnosis data for diagnose mode
        diagnosisData: identificationData as DiagnosisData,
        // Include database ID if saved
        diagnosisId: savedDiagnosis?.id,
      };

      setResults(prev => [result, ...prev]);
      setDiagnoseResult(result);
      setCurrentResult(result); // Keep for backward compatibility

      return result;
    } catch (error) {
      console.error('Plant diagnosis failed:', error);
      throw error;
    } finally {
      setIsDiagnosing(false);
      setIsIdentifying(false);
    }
  };

  // Legacy method for backward compatibility
  const identifyPlant = async (imageUri: string, problemDescription?: string, location?: string): Promise<IdentificationResult> => {
    // Use diagnosis mode if problem description is provided
    return problemDescription
      ? diagnosePlantForDiagnose(imageUri, problemDescription, location)
      : identifyPlantForScan(imageUri, location);
  };

  const clearCurrentResult = () => {
    setCurrentResult(null);
  };

  const clearScanResult = () => {
    setScanResult(null);
  };

  const clearDiagnoseResult = () => {
    setDiagnoseResult(null);
  };

  return {
    results,
    currentResult,
    scanResult,
    diagnoseResult,
    isIdentifying,
    isScanning,
    isDiagnosing,
    recentIdentifications,
    recentDiagnoses,
    isLoadingRecent,
    isLoadingRecentDiagnoses,
    identifyPlant, // Legacy method
    identifyPlantForScan,
    diagnosePlantForDiagnose,
    refreshRecentIdentifications,
    refreshRecentDiagnoses,
    clearCurrentResult,
    clearScanResult,
    clearDiagnoseResult,
  };
});
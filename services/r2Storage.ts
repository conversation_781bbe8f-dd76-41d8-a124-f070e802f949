import Constants from 'expo-constants';

// R2 Configuration from environment variables
const R2_CONFIG = {
  ACCOUNT_ID: Constants.expoConfig?.extra?.cloudflareAccountId || process.env.CLOUDFLARE_ACCOUNT_ID,
  ACCESS_KEY_ID: Constants.expoConfig?.extra?.r2AccessKeyId || process.env.R2_ACCESS_KEY_ID,
  SECRET_ACCESS_KEY: Constants.expoConfig?.extra?.r2SecretAccessKey || process.env.R2_SECRET_ACCESS_KEY,
  BUCKET_NAME: Constants.expoConfig?.extra?.r2BucketName || process.env.R2_BUCKET_NAME,
  ENDPOINT_URL: Constants.expoConfig?.extra?.r2EndpointUrl || process.env.R2_ENDPOINT_URL,
};

// Validate R2 configuration
const validateR2Config = (): boolean => {
  const required = ['ACCOUNT_ID', 'ACCESS_KEY_ID', 'SECRET_ACCESS_KEY', 'BUCKET_NAME', 'ENDPOINT_URL'];
  const missing = required.filter(key => !R2_CONFIG[key as keyof typeof R2_CONFIG]);
  
  if (missing.length > 0) {
    console.warn('Missing R2 configuration:', missing);
    return false;
  }
  
  return true;
};

// Simplified R2 Client using presigned URLs (requires backend support)
// For now, we'll implement a direct upload approach
class R2Client {
  private config = R2_CONFIG;

  async uploadFile(
    key: string,
    file: Blob,
    contentType: string = 'application/octet-stream'
  ): Promise<{ success: boolean; url?: string; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      // For now, we'll use a simple approach that requires CORS to be configured on R2
      // In production, you should use presigned URLs or a backend service
      console.log('R2 direct upload not implemented yet - falling back to Supabase');

      return {
        success: false,
        error: 'R2 direct upload requires backend implementation for security',
      };
    } catch (error) {
      console.error('R2 upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  async deleteFile(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!validateR2Config()) {
        throw new Error('R2 configuration is incomplete');
      }

      console.log('R2 delete not implemented yet - falling back to Supabase');

      return {
        success: false,
        error: 'R2 delete requires backend implementation for security',
      };
    } catch (error) {
      console.error('R2 delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }
}

// Storage service interface
export interface StorageUploadResult {
  success: boolean;
  url?: string;
  error?: string;
}

export interface StorageDeleteResult {
  success: boolean;
  error?: string;
}

// Main storage service that can fallback between R2 and Supabase
export class StorageService {
  private static r2Client = new R2Client();

  // Configuration option to disable storage uploads (useful for debugging)
  private static ENABLE_STORAGE_UPLOAD = true;

  static async uploadImage(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    // Check if storage uploads are disabled
    if (!this.ENABLE_STORAGE_UPLOAD) {
      console.log('Storage uploads disabled, using data URL fallback');
      return this.uploadAsDataUrl(file, 5 * 1024 * 1024); // 5MB limit when disabled
    }

    // Try R2 first if configured
    if (validateR2Config()) {
      console.log('Uploading to Cloudflare R2...');
      const result = await this.r2Client.uploadFile(path, file, contentType);
      if (result.success) {
        return result;
      }
      console.warn('R2 upload failed, falling back to Supabase:', result.error);
    }

    // Fallback to Supabase storage
    console.log('Uploading to Supabase storage...');
    const supabaseResult = await this.uploadToSupabase(file, path, contentType);

    if (supabaseResult.success) {
      return supabaseResult;
    }

    console.warn('Supabase upload failed, trying data URL fallback:', supabaseResult.error);

    // Final fallback to data URL for small images
    const dataUrlResult = await this.uploadAsDataUrl(file, 2 * 1024 * 1024); // 2MB limit for data URLs

    if (dataUrlResult.success) {
      console.log('Successfully created data URL fallback');
      return dataUrlResult;
    }

    // If all methods fail, return the last error
    console.error('All upload methods failed');
    return {
      success: false,
      error: `All upload methods failed. Supabase: ${supabaseResult.error}, DataURL: ${dataUrlResult.error}`,
    };
  }

  // Test Supabase storage connectivity
  private static async testSupabaseStorage(): Promise<boolean> {
    try {
      const { supabase } = await import('@/lib/supabase');

      // Try to list buckets to test connectivity
      const { data, error } = await supabase.storage.listBuckets();

      if (error) {
        console.error('Supabase storage test failed:', error);
        return false;
      }

      console.log('Supabase storage test successful, available buckets:', data?.map(b => b.name));
      return true;
    } catch (error) {
      console.error('Supabase storage test error:', error);
      return false;
    }
  }

  private static async uploadToSupabase(
    file: Blob,
    path: string,
    contentType?: string
  ): Promise<StorageUploadResult> {
    try {
      const { supabase } = await import('@/lib/supabase');

      console.log('Attempting Supabase upload:', { path, size: file.size, type: file.type });

      // Test connectivity first
      const isConnected = await this.testSupabaseStorage();
      if (!isConnected) {
        throw new Error('Supabase storage is not accessible');
      }

      const { data, error } = await supabase.storage
        .from('user-content')
        .upload(path, file, {
          contentType: contentType || file.type,
          upsert: true
        });

      if (error) {
        console.error('Supabase storage error details:', error);
        throw error;
      }

      console.log('Supabase upload successful:', data);

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('user-content')
        .getPublicUrl(path);

      console.log('Generated public URL:', publicUrl);

      return {
        success: true,
        url: publicUrl,
      };
    } catch (error) {
      console.error('Supabase upload error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown upload error',
      };
    }
  }

  // Fallback to data URL storage for small images
  private static async uploadAsDataUrl(
    file: Blob,
    maxSize: number = 1024 * 1024 // 1MB default
  ): Promise<StorageUploadResult> {
    try {
      if (file.size > maxSize) {
        return {
          success: false,
          error: `File too large for data URL storage (${file.size} bytes > ${maxSize} bytes)`,
        };
      }

      const reader = new FileReader();
      const dataUrl = await new Promise<string>((resolve, reject) => {
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });

      console.log('Created data URL fallback for image');

      return {
        success: true,
        url: dataUrl,
      };
    } catch (error) {
      console.error('Data URL creation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create data URL',
      };
    }
  }

  static async deleteImage(path: string): Promise<StorageDeleteResult> {
    // Try R2 first if configured
    if (validateR2Config()) {
      const result = await this.r2Client.deleteFile(path);
      if (result.success) {
        return result;
      }
    }

    // Fallback to Supabase storage
    try {
      const { supabase } = await import('@/lib/supabase');
      
      const { error } = await supabase.storage
        .from('user-content')
        .remove([path]);

      if (error) {
        throw error;
      }

      return { success: true };
    } catch (error) {
      console.error('Storage delete error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delete error',
      };
    }
  }

  // Helper method to generate unique file paths
  static generateImagePath(userId: string, type: 'avatar' | 'scan' | 'diagnosis', extension: string = 'jpg'): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    return `${type}s/${userId}-${timestamp}-${random}.${extension}`;
  }

  // Configuration methods
  static enableStorageUploads(enabled: boolean = true): void {
    this.ENABLE_STORAGE_UPLOAD = enabled;
    console.log(`Storage uploads ${enabled ? 'enabled' : 'disabled'}`);
  }

  static isStorageUploadEnabled(): boolean {
    return this.ENABLE_STORAGE_UPLOAD;
  }

  // Health check method
  static async checkStorageHealth(): Promise<{
    r2Available: boolean;
    supabaseAvailable: boolean;
    recommendedMethod: 'r2' | 'supabase' | 'dataurl';
  }> {
    const r2Available = validateR2Config();
    const supabaseAvailable = await this.testSupabaseStorage();

    let recommendedMethod: 'r2' | 'supabase' | 'dataurl' = 'dataurl';

    if (r2Available) {
      recommendedMethod = 'r2';
    } else if (supabaseAvailable) {
      recommendedMethod = 'supabase';
    }

    console.log('Storage health check:', {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    });

    return {
      r2Available,
      supabaseAvailable,
      recommendedMethod,
    };
  }
}
